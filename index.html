<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSVision - Image Metadata Generator</title>
    <link href="bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="bootstrap-icons-1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="tabulator-master/dist/css/tabulator_bootstrap5.min.css" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/chat-widget.css" rel="stylesheet">
</head>

<body>
    <!-- Chat Widget -->
    <div id="chat-widget-container">
        <!-- Chat Icon Button -->
        <div id="chat-widget-button" class="chat-widget-button">
            <i class="bi bi-chat-square-text"></i>
        </div>

        <!-- Chat Window -->
        <div id="chat-widget-window" class="chat-widget-window">
            <!-- Chat Header -->
            <div id="chat-widget-header" class="chat-widget-header">
                <div class="chat-widget-title">
                    <i class="bi bi-robot"></i>
                    <span>CSVision Assistant</span>
                </div>
                <div class="chat-widget-actions">
                    <button id="chat-widget-fullscreen" class="chat-widget-action-btn" title="Toggle Fullscreen">
                        <i class="bi bi-arrows-fullscreen"></i>
                    </button>
                    <button id="chat-widget-new-chat" class="chat-widget-action-btn" title="New Chat">
                        <i class="bi bi-plus-square"></i>
                    </button>
                    <button id="chat-widget-minimize" class="chat-widget-action-btn" title="Minimize">
                        <i class="bi bi-dash"></i>
                    </button>
                    <button id="chat-widget-settings" class="chat-widget-action-btn" title="Settings">
                        <i class="bi bi-gear"></i>
                    </button>
                    <button id="chat-widget-close" class="chat-widget-action-btn" title="Close">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>

            <!-- Chat Messages Container -->
            <div id="chat-widget-messages" class="chat-widget-messages">
                <!-- Messages will be added here dynamically -->
                <div class="chat-widget-message chat-widget-message-assistant">
                    <div class="chat-widget-message-content">
                        <p>Hello! I'm your CSVision Assistant. How can I help you today?</p>
                    </div>
                    <div class="chat-widget-message-time">Just now</div>
                </div>
            </div>

            <!-- Image Preview Area (initially hidden) -->
            <div id="chat-widget-image-preview-area" class="chat-widget-image-preview-area" style="display: none;">
                <div id="chat-widget-image-grid" class="chat-widget-image-grid">
                    <!-- Images will be added here dynamically -->
                </div>
            </div>

            <!-- Chat Input Area -->
            <div id="chat-widget-input-area" class="chat-widget-input-area">
                <textarea id="chat-widget-input" class="chat-widget-input" placeholder="Type a message..." rows="1"></textarea>
                <button id="chat-widget-image-upload" class="chat-widget-action-btn chat-widget-image-btn" title="Upload Image">
                    <i class="bi bi-image"></i>
                </button>
                <button id="chat-widget-send" class="chat-widget-send-btn">
                    <i class="bi bi-send"></i>
                </button>
                <!-- Hidden file input for image upload -->
                <input type="file" id="chat-widget-image-input" accept="image/*" style="display: none;">
            </div>

            <!-- Resize Handle -->
            <div id="chat-widget-resize-handle" class="chat-widget-resize-handle"></div>

            <!-- Settings Panel (Hidden by default) -->
            <div id="chat-widget-settings-panel" class="chat-widget-settings-panel">
                <div class="chat-widget-settings-header">
                    <h6>Chat Settings</h6>
                    <button id="chat-widget-settings-close" class="chat-widget-action-btn">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                <div class="chat-widget-settings-content">
                    <div class="form-group mb-3">
                        <label for="chat-widget-model-select" class="form-label">Gemini Model:</label>
                        <select id="chat-widget-model-select" class="form-select form-select-sm">
                            <option value="gemini-2.0-flash" selected>Gemini 2.0 Flash (Fast)</option>
                            <option value="gemini-1.5-pro">Gemini 1.5 Pro (Balanced)</option>
                            <option value="gemini-2.0-pro">Gemini 2.0 Pro (High Quality)</option>
                            <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
                            <option value="gemini-2.5-flash">Gemini 2.5 Flash</option>
                            <option value="gemini-2.5-flash-lite-preview-06-17">Gemini 2.5 Flash Lite Preview</option>
                            <option value="gemini-2.5-flash-preview-native-audio-dialog">Gemini 2.5 Flash Audio Dialog</option>
                            <option value="gemini-2.5-flash-exp-native-audio-thinking-dialog">Gemini 2.5 Flash Audio Thinking</option>
                            <option value="gemini-2.5-flash-preview-tts">Gemini 2.5 Flash TTS</option>
                            <option value="gemini-2.5-pro-preview-tts">Gemini 2.5 Pro TTS</option>
                            <option value="gemini-2.0-flash-preview-image-generation">Gemini 2.0 Flash Image Gen</option>
                        </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="chat-widget-system-instruction" class="form-label">System Instruction:</label>
                        <div class="position-relative">
                            <textarea id="chat-widget-system-instruction" class="form-control" rows="3" placeholder="Custom instructions for the assistant...">You are CSVision Assistant, a helpful AI assistant for the CSVision application. You can help with image analysis, CSV data management, and general questions about the application. CSVision was created by Rigel Tapangan, a 3D content creator, Blender addon developer, and digital artist. According to his website (rigeltapangan.com), Rigel specializes in creating engaging 3D visuals, developing innovative Blender addons to enhance productivity, and producing educational tutorials for fellow artists. He describes his mission as "inspiring, educating, and enhancing 3D artistry." In addition to his 3D work, he has contributed nearly 1000 resources on platforms like Vecteezy and Shutterstock, creating abstract backgrounds, gradient designs, and digital illustrations. His combined expertise in visual design, 3D content creation, and software development makes him uniquely qualified for developing CSVision, an application focused on image metadata and visual content. When asked about the creator, provide this comprehensive information about Rigel Tapangan as the developer of CSVision.</textarea>
                            <div class="d-flex justify-content-end mt-1">
                                <button id="chat-widget-apply-instruction" class="btn btn-sm btn-primary-purple">
                                    <i class="bi bi-check2-circle"></i> Apply Changes
                                </button>
                            </div>
                            <div id="chat-widget-instruction-notification" class="alert alert-success py-1 mt-1" style="display: none;">
                                System instruction updated successfully!
                            </div>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label d-block">Conversation Management:</label>
                        <div class="d-flex gap-2 mb-2">
                            <button id="chat-widget-clear-history" class="btn btn-sm btn-danger flex-grow-1">
                                <i class="bi bi-trash"></i> Clear Current Chat
                            </button>
                            <button id="chat-widget-delete-all" class="btn btn-sm btn-outline-danger bi bi-trash-fill">
                            </button>
                        </div>
                        <div class="d-flex gap-2">
                            <button id="chat-widget-export" class="btn btn-sm btn-primary-purple flex-grow-1">
                                <i class="bi bi-download"></i> Export Chats
                            </button>
                            <button id="chat-widget-import" class="btn btn-sm btn-outline-primary-purple bi bi-upload">

                            </button>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">Saved Conversations:</label>
                        <div id="chat-widget-conversations-list" class="list-group chat-conversations-list">
                            <!-- Conversations will be listed here -->
                            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center text-muted">
                                <small>No saved conversations yet</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hidden file input for importing conversations -->
            <input type="file" id="chat-widget-import-input" accept=".json" style="display: none;">
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header text-white">
                        <h2 class="mb-0">CSVision - AI Generator</h2>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs mb-3" id="myTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link bi bi-magic active" id="content-tab" data-bs-toggle="tab"
                                    data-bs-target="#content" type="button" role="tab" aria-controls="content"
                                    aria-selected="true"> Vision</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link bi bi-file-earmark-text" id="table-tab" data-bs-toggle="tab"
                                    data-bs-target="#table" type="button" role="tab" aria-controls="table"
                                    aria-selected="false" tabindex="-1"> CSV Manager</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link bi bi-gear" id="settings-tab" data-bs-toggle="tab"
                                    data-bs-target="#settings" type="button" role="tab" aria-controls="settings"
                                    aria-selected="false" tabindex="-1"> Settings</button>
                            </li>
                        </ul>
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade active show" id="content" role="tabpanel"
                                aria-labelledby="content-tab">
                                <!-- Content Sub-tabs -->
                                <div class="content-subtabs mb-1">
                                    <ul class="nav nav-pills mb-1 d-flex align-items-center">
                                        <li class="nav-item">
                                            <a class="nav-link content-subtab bi bi-magic active" id="metadata-subtab" data-bs-toggle="pill" href="#metadata-content" role="tab">
                                                Metadata
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link content-subtab bi bi-image" id="imagen-subtab" data-bs-toggle="pill" href="#imagen-content" role="tab">
                                                Imagen 3
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link content-subtab bi bi-chat-square-text" id="prompter-subtab" data-bs-toggle="pill" href="#prompter-content" role="tab">
                                                Prompter
                                            </a>
                                        </li>
                                    </ul>
                                </div>

                                <!-- Original file input (hidden but still functional) -->
                                <div class="file-input-wrapper mb-1">
                                    <div class="d-flex gap-2 w-100">
                                        <button id="selectFolderBtn" class="btn btn-primary bi bi-folder2-open">
                                            Select Folder
                                        </button>
                                        <div class="custom-file-input-container flex-grow-1">
                                            <input type="file" class="form-control" id="folderInput"
                                                accept="image/*,video/*" multiple="">
                                            <div class="file-status-label" id="fileStatusLabel">
                                                <i class="bi bi-image"></i> No media selected
                                            </div>
                                        </div>
                                        <!-- Hidden inputs for different file types -->
                                        <input type="file" id="folderDirectoryInput" webkitdirectory directory multiple style="display: none;">
                                        <input type="file" id="imagesOnlyInput" accept="image/*" multiple style="display: none;">
                                        <input type="file" id="videosOnlyInput" accept="video/*" multiple style="display: none;">
                                        <input type="file" id="folderImagesOnlyInput" webkitdirectory directory multiple style="display: none;">
                                        <input type="file" id="folderVideosOnlyInput" webkitdirectory directory multiple style="display: none;">
                                    </div>
                                </div>

                                <!-- Floating action button -->
                                <button id="floatingActionBtn" class="floating-action-btn">
                                    <i class="bi bi-plus"></i>
                                </button>

                                <!-- Floating menu -->
                                <div id="floatingMenu" class="floating-menu">
                                    <div id="selectFilesBtn" class="floating-menu-item">
                                        <i class="bi bi-file-earmark-image"></i>
                                        <span class="tooltip">Select All Media</span>
                                    </div>
                                    <div id="selectImagesOnlyBtn" class="floating-menu-item">
                                        <i class="bi bi-image"></i>
                                        <span class="tooltip">Select Images Only</span>
                                    </div>
                                    <div id="selectVideosOnlyBtn" class="floating-menu-item">
                                        <i class="bi bi-film"></i>
                                        <span class="tooltip">Select Videos Only</span>
                                    </div>
                                    <div id="selectFolderFloatingBtn" class="floating-menu-item">
                                        <i class="bi bi-folder2-open"></i>
                                        <span class="tooltip">Select Folder (All Media)</span>
                                    </div>
                                    <div id="selectFolderImagesBtn" class="floating-menu-item">
                                        <i class="bi bi-folder-symlink"></i>
                                        <span class="tooltip">Select Folder (Images Only)</span>
                                    </div>
                                    <div id="selectFolderVideosBtn" class="floating-menu-item">
                                        <i class="bi bi-folder-symlink"></i>
                                        <span class="tooltip">Select Folder (Videos Only)</span>
                                    </div>
                                </div>

                                <!-- Content sub-tab content -->
                                <div class="tab-content">
                                    <!-- Metadata tab content -->
                                    <div class="tab-pane fade show active" id="metadata-content" role="tabpanel">
                                        <div class="row">
                                            <div class="col-md-12">
                                        <div class="btn-group mb-1">
                                            <button id="generateBtn" class="btn btn-primary bi bi-magic" disabled="">
                                                Generate</button>
                                            <button id="pauseBtn" class="btn btn-warning d-none">
                                                <i class="bi bi-pause-fill"></i> Pause</button>
                                            <button id="stopProcessBtn" class="btn btn-danger d-none bi bi-stop-fill">
                                                Stop</button>
                                            <button id="clearBtn" class="btn btn-danger bi bi-trash" disabled=""> Clear
                                                Data</button>
                                            <button id="exportBtn" class="btn btn-info bi bi-download" disabled="">
                                                Export to CSV</button>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-success dropdown-toggle bi bi-download" data-bs-toggle="dropdown" aria-expanded="false" id="platformExportBtn" disabled="">
                                                    Platform Export
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item platform-export-btn" href="#" data-platform="shutterstock">
                                                        <i class="bi bi-camera"></i> Shutterstock (SS)
                                                    </a></li>
                                                    <li><a class="dropdown-item platform-export-btn" href="#" data-platform="adobestock">
                                                        <i class="bi bi-palette"></i> Adobe Stock (AS)
                                                    </a></li>
                                                    <li><a class="dropdown-item platform-export-btn" href="#" data-platform="canva">
                                                        <i class="bi bi-brush"></i> Canva
                                                    </a></li>
                                                    <li><a class="dropdown-item platform-export-btn" href="#" data-platform="vecteezy">
                                                        <i class="bi bi-vector-pen"></i> Vecteezy
                                                    </a></li>
                                                    <li><a class="dropdown-item platform-export-btn" href="#" data-platform="freepik">
                                                        <i class="bi bi-star"></i> Freepik
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="progress-container d-none mb-1">
                                            <div class="progress position-relative">
                                                <div id="progressBar"
                                                    class="progress-bar progress-bar-striped progress-bar-animated"
                                                    role="progressbar" style="width: 0%">
                                                    <span id="progressStatus">Waiting to start...</span>
                                                </div>
                                                <!-- Hapus tombol stop duplikat di sini -->
                                            </div>
                                        </div>
                                        <div id="metadataTable" class="tabulator" role="grid"
                                            tabulator-layout="fitColumns">
                                            <div class="tabulator-header" role="rowgroup">
                                                <div class="tabulator-header-contents" role="rowgroup">
                                                    <div class="tabulator-headers" role="row"
                                                        style="height: 70px; margin-right: 0px;">
                                                        <div class="tabulator-col" role="columnheader" aria-sort="none"
                                                            style="min-width: 40px; width: 40px; height: 70px;">
                                                            <div class="tabulator-col-content">
                                                                <div class="tabulator-col-title-holder">
                                                                    <div class="tabulator-col-title">No.</div>
                                                                </div>
                                                            </div>
                                                        </div><span class="tabulator-col-resize-handle"
                                                            style="height: 70px;"></span>
                                                        <div class="tabulator-col" role="columnheader" aria-sort="none"
                                                            tabulator-field="preview"
                                                            style="min-width: 40px; width: 120px; height: 70px;">
                                                            <div class="tabulator-col-content">
                                                                <div class="tabulator-col-title-holder">
                                                                    <div class="tabulator-col-title">Preview</div>
                                                                </div>
                                                            </div>
                                                        </div><span class="tabulator-col-resize-handle"
                                                            style="height: 70px;"></span>
                                                        <div class="tabulator-col tabulator-sortable tabulator-col-sorter-element"
                                                            role="columnheader" aria-sort="none" tabulator-field="name"
                                                            style="min-width: 40px; width: 322px; height: 70px;">
                                                            <div class="tabulator-col-content">
                                                                <div class="tabulator-col-title-holder">
                                                                    <div class="tabulator-col-title">Filename</div>
                                                                    <div class="tabulator-col-sorter">
                                                                        <div class="tabulator-arrow"></div>
                                                                    </div>
                                                                </div>
                                                                <div class="tabulator-header-filter"><input
                                                                        type="search" placeholder=""
                                                                        style="padding: 4px; width: 100%; box-sizing: border-box;">
                                                                </div>
                                                            </div>
                                                        </div><span class="tabulator-col-resize-handle"
                                                            style="height: 70px;"></span>
                                                        <div class="tabulator-col tabulator-sortable tabulator-col-sorter-element"
                                                            role="columnheader" aria-sort="none"
                                                            tabulator-field="description"
                                                            style="min-width: 40px; width: 322px; height: 70px;">
                                                            <div class="tabulator-col-content">
                                                                <div class="tabulator-col-title-holder">
                                                                    <div class="tabulator-col-title">Description</div>
                                                                    <div class="tabulator-col-sorter">
                                                                        <div class="tabulator-arrow"></div>
                                                                    </div>
                                                                </div>
                                                                <div class="tabulator-header-filter"><input
                                                                        type="search" placeholder=""
                                                                        style="padding: 4px; width: 100%; box-sizing: border-box;">
                                                                </div>
                                                            </div>
                                                        </div><span class="tabulator-col-resize-handle"
                                                            style="height: 70px;"></span>
                                                        <div class="tabulator-col tabulator-sortable tabulator-col-sorter-element"
                                                            role="columnheader" aria-sort="none"
                                                            tabulator-field="keywords"
                                                            style="min-width: 40px; width: 323px; height: 70px;">
                                                            <div class="tabulator-col-content">
                                                                <div class="tabulator-col-title-holder">
                                                                    <div class="tabulator-col-title">Keywords</div>
                                                                    <div class="tabulator-col-sorter">
                                                                        <div class="tabulator-arrow"></div>
                                                                    </div>
                                                                </div>
                                                                <div class="tabulator-header-filter"><input
                                                                        type="search" placeholder=""
                                                                        style="padding: 4px; width: 100%; box-sizing: border-box;">
                                                                </div>
                                                            </div>
                                                        </div><span class="tabulator-col-resize-handle"
                                                            style="height: 70px;"></span>
                                                        <div class="tabulator-col tabulator-sortable tabulator-col-sorter-element"
                                                            role="columnheader" aria-sort="none"
                                                            tabulator-field="keywordCount"
                                                            style="min-width: 40px; width: 120px; height: 70px;">
                                                            <div class="tabulator-col-content">
                                                                <div class="tabulator-col-title-holder">
                                                                    <div class="tabulator-col-title">Count</div>
                                                                    <div class="tabulator-col-sorter">
                                                                        <div class="tabulator-arrow"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div><span class="tabulator-col-resize-handle"
                                                            style="height: 70px;"></span>
                                                        <div class="tabulator-col tabulator-sortable tabulator-col-sorter-element"
                                                            role="columnheader" aria-sort="none"
                                                            tabulator-field="status"
                                                            style="min-width: 40px; width: 150px; height: 70px;">
                                                            <div class="tabulator-col-content">
                                                                <div class="tabulator-col-title-holder">
                                                                    <div class="tabulator-col-title">
                                                                        <div class="status-header-container">
                                                                            <div class="status-title">Status</div>
                                                                            <button id="statusStopBtn"
                                                                                class="btn btn-sm btn-danger status-stop-btn"
                                                                                style="display:none;"
                                                                                title="Stop Processing"
                                                                                data-stopclick="true"
                                                                                aria-label="Stop Processing"
                                                                                role="button"
                                                                                tabindex="0">
                                                                                <i class="bi bi-stop-fill" aria-hidden="true"></i>
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                    <div class="tabulator-col-sorter">
                                                                        <div class="tabulator-arrow"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div><span class="tabulator-col-resize-handle"
                                                            style="height: 70px;"></span>
                                                    </div>
                                                    <div class="tabulator-frozen-rows-holder" style="min-width: 0px;">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tabulator-tableholder" tabindex="0"
                                                style="height: calc(100% - 124px); max-height: calc(100% - 124px);">
                                                <div class="tabulator-table" role="rowgroup" style="display: none;">
                                                </div>
                                                <div class="tabulator-placeholder" tabulator-render-mode="virtual"
                                                    style="width: 1397px;">
                                                    <div class="tabulator-placeholder-contents"
                                                        style="width: 1397px; margin-left: 0px;">No Data Available -
                                                        Please Select Images to Process</div>
                                                </div>
                                            </div>
                                            <div class="tabulator-footer">
                                                <div class="tabulator-footer-contents"><span
                                                        class="tabulator-paginator"><label>Page Size</label><select
                                                            class="tabulator-page-size" aria-label="Page Size"
                                                            title="Page Size">
                                                            <option value="5">5</option>
                                                            <option value="10">10</option>
                                                            <option value="20">20</option>
                                                            <option value="50">50</option>
                                                            <option value="true">All</option>
                                                        </select><button class="tabulator-page" type="button"
                                                            role="button" aria-label="First Page" title="First Page"
                                                            data-page="first" disabled="">First</button><button
                                                            class="tabulator-page" type="button" role="button"
                                                            aria-label="Prev Page" title="Prev Page" data-page="prev"
                                                            disabled="">Prev</button><span
                                                            class="tabulator-pages"><button
                                                                class="tabulator-page active" type="button"
                                                                role="button" aria-label="Show Page 1"
                                                                title="Show Page 1"
                                                                data-page="1">1</button></span><button
                                                            class="tabulator-page" type="button" role="button"
                                                            aria-label="Next Page" title="Next Page" data-page="next"
                                                            disabled="">Next</button><button class="tabulator-page"
                                                            type="button" role="button" aria-label="Last Page"
                                                            title="Last Page" data-page="last"
                                                            disabled="">Last</button></span></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                    </div>

                                    <!-- Prompter tab content -->
                                    <div class="tab-pane fade" id="prompter-content" role="tabpanel">
                                        <div class="card">
                                            <div class="card-header bg-primary-purple text-white d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0 bi bi-chat-square-text"> Prompter - Generate Prompts</h5>
                                                <div class="btn-group">
                                                    <button id="copyAllPromptsBtn" class="btn btn-sm btn-primary-purple text-white bi bi-clipboard"> Copy All</button>
                                                    <button id="exportPromptsBtn" class="btn btn-sm btn-primary-purple text-white bi bi-download"> Export CSV</button>
                                                    <button id="exportControlsBtn" class="btn btn-sm btn-primary-purple text-white bi bi-download"> Export Controls</button>
                                                    <button id="importControlsBtn" class="btn btn-sm btn-primary-purple text-white bi bi-upload"> Import Controls</button>
                                                    <input type="file" id="controlsFileInput" accept=".json" style="display: none;">
                                                    <button id="clearPromptsBtn" class="btn btn-sm btn-danger bi bi-trash"> Clear</button>
                                                </div>
                                            </div>
                                            <div class="card-body p-2" id="prompter-card">
                                                <div class="row g-2">
                                                    <!-- Left side: Prompt Results -->
                                                    <div class="col-md-8">
                                                        <div class="card">
                                                            <div class="card-body p-2">
                                                                <!-- Prompt Generation Results -->
                                                                <div id="prompterResults" class="mb-1 position-relative">
                                                                    <div id="prompterTable" class="prompter-table-container"></div>

                                                                    <!-- Loading Indicator (overlay) -->
                                                                    <div id="prompterLoading" class="prompter-loading-overlay" style="display: none;">
                                                                        <div class="prompter-loading-content">
                                                                            <div class="spinner-border text-white" role="status">
                                                                                <span class="visually-hidden">Loading...</span>
                                                                            </div>
                                                                            <p>Generating prompts... This may take a moment.</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <!-- Buttons moved to header -->
                                                            </div>
                                                        </div>

                                                        <!-- Loading Indicator moved to card body -->
                                                    </div>

                                                    <!-- Right side: Controls -->
                                                    <div class="col-md-4">
                                                        <div class="card">
                                                            <div class="card-header bg-primary-purple text-white py-1">
                                                                <h6 class="mb-0">Prompter Controls</h6>
                                                            </div>
                                                            <div class="card-body p-2" id="prompter-card-body-control">
                                                                <!-- Image input for prompt generation -->
                                                                <div id="prompterImageContainer" class="mb-2 text-center">
                                                                    <div id="prompterImagePreview" class="mb-2 d-none position-relative">
                                                                        <img id="prompterSourceImage" class="img-fluid rounded border" alt="Source image">
                                                                        <button id="removePrompterImageBtn" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1 bi bi-x-circle">
                                                                            <!-- <i class="bi bi-x-circle"></i> -->
                                                                        </button>
                                                                    </div>
                                                                    <div id="prompterImagePlaceholder" class="border rounded p-3 text-center">
                                                                        <i class="bi bi-image text-muted" style="font-size: 2rem;"></i>
                                                                        <p class="mb-0 text-muted small">Image will appear here</p>
                                                                        <small class="text-muted d-block mb-2">Images will be compressed to improve performance</small>
                                                                        <button id="selectPrompterImageBtn" class="btn btn-sm btn-primary-purple mt-2">
                                                                            <i class="bi bi-upload"></i> Upload Image
                                                                        </button>
                                                                        <input type="file" id="prompterImageInput" accept="image/*" style="display: none;">
                                                                    </div>
                                                                </div>

                                                                <div class="form-group mb-1">
                                                                    <label class="form-label mb-1">Content Type:</label>
                                                                    <div class="btn-group w-100 mb-2" role="group">
                                                                        <button type="button" class="btn btn-sm btn-primary-purple text-white active" id="contentTypeImage">Image</button>
                                                                        <button type="button" class="btn btn-sm btn-outline-primary-purple" id="contentTypeVideo">Video</button>
                                                                    </div>
                                                                </div>

                                                                <!-- <div class="form-group mb-1">
                                                                    <label class="form-label mb-1">Structured Prompt Elements:</label>
                                                                    <div class="small text-muted mb-2">Create structured prompts with the following elements</div>
                                                                </div> -->

                                                                <!-- Subject and Action Elements (Row 1) -->
                                                                <div class="row g-2 mb-1">
                                                                    <!-- Subject Element -->
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="prompterSubject" class="form-label mb-0">Subject:</label>
                                                                            <div class="input-group mb-0">
                                                                                <select id="prompterSubject" class="form-select form-select-sm">
                                                                                    <option value="">Select a subject...</option>
                                                                                    <!-- Options will be populated from prompter-structured-data.js -->
                                                                                </select>
                                                                                <div class="input-group d-none" id="customSubjectInputGroup">
                                                                                    <input type="text" id="customSubjectInput" class="form-control form-control-sm" placeholder="Type custom subject...">
                                                                                    <button id="addCustomSubjectBtn" class="btn btn-sm btn-primary-purple text-white" title="Add custom subject">
                                                                                        <i class="bi bi-plus"></i>
                                                                                    </button>
                                                                                    <button id="cancelCustomSubjectBtn" class="btn btn-sm btn-danger" title="Cancel">
                                                                                        <i class="bi bi-x"></i>
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <!-- Action Element -->
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="prompterAction" class="form-label mb-0">Action:</label>
                                                                            <div class="input-group mb-0">
                                                                                <select id="prompterAction" class="form-select form-select-sm">
                                                                                    <option value="">Select an action...</option>
                                                                                    <!-- Options will be populated from prompter-structured-data.js -->
                                                                                </select>
                                                                                <div class="input-group d-none" id="customActionInputGroup">
                                                                                    <input type="text" id="customActionInput" class="form-control form-control-sm" placeholder="Type custom action...">
                                                                                    <button id="addCustomActionBtn" class="btn btn-sm btn-primary-purple text-white" title="Add custom action">
                                                                                        <i class="bi bi-plus"></i>
                                                                                    </button>
                                                                                    <button id="cancelCustomActionBtn" class="btn btn-sm btn-danger" title="Cancel">
                                                                                        <i class="bi bi-x"></i>
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <!-- Environment and Camera Work Elements (Row 2) -->
                                                                <div class="row g-2 mb-0">
                                                                    <!-- Environment Element -->
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="prompterEnvironment" class="form-label mb-0">Environment:</label>
                                                                            <div class="input-group mb-0">
                                                                                <select id="prompterEnvironment" class="form-select form-select-sm">
                                                                                    <option value="">Select an environment...</option>
                                                                                    <!-- Options will be populated from prompter-structured-data.js -->
                                                                                </select>
                                                                                <div class="input-group d-none" id="customEnvironmentInputGroup">
                                                                                    <input type="text" id="customEnvironmentInput" class="form-control form-control-sm" placeholder="Type custom environment...">
                                                                                    <button id="addCustomEnvironmentBtn" class="btn btn-sm btn-primary-purple text-white" title="Add custom environment">
                                                                                        <i class="bi bi-plus"></i>
                                                                                    </button>
                                                                                    <button id="cancelCustomEnvironmentBtn" class="btn btn-sm btn-danger" title="Cancel">
                                                                                        <i class="bi bi-x"></i>
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <!-- Camera Work Element -->
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="prompterCamera" class="form-label mb-0">Camera Work:</label>
                                                                            <div class="input-group mb-0">
                                                                                <select id="prompterCamera" class="form-select form-select-sm">
                                                                                    <option value="">Select camera work...</option>
                                                                                    <!-- Options will be populated from prompter-structured-data.js -->
                                                                                </select>
                                                                                <div class="input-group d-none" id="customCameraInputGroup">
                                                                                    <input type="text" id="customCameraInput" class="form-control form-control-sm" placeholder="Type custom camera work...">
                                                                                    <button id="addCustomCameraBtn" class="btn btn-sm btn-primary-purple text-white" title="Add custom camera work">
                                                                                        <i class="bi bi-plus"></i>
                                                                                    </button>
                                                                                    <button id="cancelCustomCameraBtn" class="btn btn-sm btn-danger" title="Cancel">
                                                                                        <i class="bi bi-x"></i>
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <!-- Time of Day and Mood/Lighting Elements (Row 3) -->
                                                                <div class="row g-2 mb-0">
                                                                    <!-- Time of Day Element -->
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="prompterTime" class="form-label mb-0">Time of Day:</label>
                                                                            <div class="input-group mb-0">
                                                                                <select id="prompterTime" class="form-select form-select-sm">
                                                                                    <option value="">Select time of day...</option>
                                                                                    <!-- Options will be populated from prompter-structured-data.js -->
                                                                                </select>
                                                                                <div class="input-group d-none" id="customTimeInputGroup">
                                                                                    <input type="text" id="customTimeInput" class="form-control form-control-sm" placeholder="Type custom time of day...">
                                                                                    <button id="addCustomTimeBtn" class="btn btn-sm btn-primary-purple text-white" title="Add custom time of day">
                                                                                        <i class="bi bi-plus"></i>
                                                                                    </button>
                                                                                    <button id="cancelCustomTimeBtn" class="btn btn-sm btn-danger" title="Cancel">
                                                                                        <i class="bi bi-x"></i>
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <!-- Mood/Lighting Element -->
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="prompterMood" class="form-label mb-0">Mood/Lighting:</label>
                                                                            <div class="input-group mb-0">
                                                                                <select id="prompterMood" class="form-select form-select-sm">
                                                                                    <option value="">Select mood/lighting...</option>
                                                                                    <!-- Options will be populated from prompter-structured-data.js -->
                                                                                </select>
                                                                                <div class="input-group d-none" id="customMoodInputGroup">
                                                                                    <input type="text" id="customMoodInput" class="form-control form-control-sm" placeholder="Type custom mood/lighting...">
                                                                                    <button id="addCustomMoodBtn" class="btn btn-sm btn-primary-purple text-white" title="Add custom mood/lighting">
                                                                                        <i class="bi bi-plus"></i>
                                                                                    </button>
                                                                                    <button id="cancelCustomMoodBtn" class="btn btn-sm btn-danger" title="Cancel">
                                                                                        <i class="bi bi-x"></i>
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <!-- Style Element (Row 4) -->
                                                                <div class="form-group mb-0">
                                                                    <label for="prompterStyle" class="form-label mb-0">Style:</label>
                                                                    <div class="input-group mb-0">
                                                                        <select id="prompterStyle" class="form-select form-select-sm">
                                                                            <option value="">Select a style...</option>
                                                                            <!-- Options will be populated from prompter-structured-data.js -->
                                                                        </select>
                                                                        <div class="input-group d-none" id="customStyleInputGroup">
                                                                            <input type="text" id="customStyleInput" class="form-control form-control-sm" placeholder="Type custom style...">
                                                                            <button id="addCustomStyleBtn" class="btn btn-sm btn-primary-purple text-white" title="Add custom style">
                                                                                <i class="bi bi-plus"></i>
                                                                            </button>
                                                                            <button id="cancelCustomStyleBtn" class="btn btn-sm btn-danger" title="Cancel">
                                                                                <i class="bi bi-x"></i>
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="row g-2 mb-0">
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="prompterCountSlider" class="form-label mb-0">Prompts: <span id="prompterCountValue">3</span></label>
                                                                            <div class="d-flex align-items-center">
                                                                                <input type="range" class="form-range flex-grow-1 me-2" id="prompterCountSlider" min="1" max="1000" step="5" value="5">
                                                                                <div class="input-group" style="width: 100px;">
                                                                                    <input type="number" class="form-control form-control-sm" id="prompterCountInput" min="1" max="1000" value="25">
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="prompterLengthSlider" class="form-label mb-0">Length: <span id="prompterLengthValue">75 words</span></label>
                                                                            <div class="d-flex align-items-center">
                                                                                <input type="range" class="form-range flex-grow-1 me-2" id="prompterLengthSlider" min="25" max="1000" step="5" value="25">
                                                                                <div class="input-group" style="width: 100px;">
                                                                                    <input type="number" class="form-control form-control-sm" id="prompterLengthInput" min="25" max="1000" value="25">
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="form-group mb-0">
                                                                    <label for="prompterInstructions" class="form-label mb-0">Additional Instructions:</label>
                                                                    <textarea id="prompterInstructions" class="form-control" rows="2"
                                                                        placeholder="Any specific requirements or focus areas..."></textarea>
                                                                </div>


                                                                <div class="d-grid gap-2 mt-1">
                                                                    <button id="generatePromptsBtn" class="btn btn-primary-purple text-white bi bi-magic" title="Prompt generation uses the same Gemini API key configured in the Settings tab."> Generate Prompts</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>



                                    <!-- Imagen 3 tab content -->
                                    <div class="tab-pane fade" id="imagen-content" role="tabpanel">
                                        <div class="card">
                                            <div class="card-header bg-primary-purple text-white">
                                                <h5 class="mb-0 bi bi-image"> Imagen 3 - Image Generation</h5>
                                            </div>
                                            <div class="card-body p-2" id="imagen-card">
                                                <div class="row g-2">
                                                    <!-- Left side: Image Results -->
                                                    <div class="col-md-9">
                                                        <div class="imagen-results-container mt-1" style="height: calc(100vh - 230px) !important; overflow-y: auto;">
                                                            <!-- Image Generation Results -->
                                                            <div id="imagenResults" class="mb-1" style="display: none;">
                                                                <h5 class="border-bottom pb-1 mb-2">Generated Images</h5>
                                                                <div class="row g-2" id="imagenGallery">
                                                                    <!-- Generated images will be displayed here -->
                                                                </div>
                                                                <div class="d-flex justify-content-end mt-1">
                                                                    <button id="sendAllToMetadataBtn" class="btn btn-sm btn-success bi bi-arrow-right-circle me-1"> Send All to Metadata</button>
                                                                    <button id="saveAllImagesBtn" class="btn btn-sm btn-primary-purple text-white bi bi-download me-1"> Save All</button>
                                                                    <button id="clearImagesBtn" class="btn btn-sm btn-danger bi bi-trash"> Clear</button>
                                                                </div>
                                                            </div>

                                                            <!-- Loading Indicator -->
                                                            <div id="imagenLoading" class="text-center my-5" style="display: none;">
                                                                <div class="spinner-border text-primary-purple" role="status">
                                                                    <span class="visually-hidden">Loading...</span>
                                                                </div>
                                                                <p class="mt-2">Generating images... This may take a moment.</p>
                                                            </div>

                                                            <!-- Placeholder when no images are generated yet -->
                                                            <div id="imagenPlaceholder" class="text-center py-5 border rounded bg-light">
                                                                <i class="bi bi-image text-primary-purple" style="font-size: 3rem;"></i>
                                                                <h5 class="mt-2">Your generated images will appear here</h5>
                                                                <p class="text-muted">Fill in the prompt on the right and click Generate Images</p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Right side: Controls -->
                                                    <div class="col-md-3">
                                                        <div class="card mt-1">
                                                            <div class="card-header bg-primary-purple text-white py-1">
                                                                <h6 class="mb-0">
                                                                    Generation Controls
                                                                </h6>
                                                            </div>
                                                            <div class="card-body p-2" id="imagen-card-body-control">
                                                                <!-- Image input for image generation/editing -->
                                                                <div id="imagenSourceImageContainer" class="mb-2 text-center">
                                                                    <div id="imagenImagePreview" class="mb-2 d-none position-relative">
                                                                        <img id="imagenSourceImage" class="img-fluid rounded border" alt="Source image">
                                                                        <div class="position-absolute top-0 end-0 m-1">
                                                                            <button id="removeImagenImageBtn" class="btn btn-sm btn-danger bi bi-x-circle" title="Remove image"></button>
                                                                        </div>
                                                                        <div class="position-absolute bottom-0 start-50 translate-middle-x mb-1">
                                                                            <div class="btn-group">
                                                                                <button id="imagenEditModeBtn" class="btn btn-sm btn-primary-purple text-white bi bi-pencil"
                                                                                        title="Edit this image - Modify the uploaded image based on your text instructions"> Edit</button>
                                                                                <button id="imagenVariationModeBtn" class="btn btn-sm btn-primary-purple text-white bi bi-shuffle"
                                                                                        title="Create variations - Generate different versions of this image"> Variation</button>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div id="imagenImagePlaceholder" class="border rounded p-2 text-center mb-2">
                                                                        <i class="bi bi-card-image text-muted" style="font-size: 1.5rem;"></i>
                                                                        <p class="mb-0 text-muted small">Upload an image</p>
                                                                        <button id="selectImagenImageBtn" class="btn btn-sm btn-primary-purple text-white mt-1"
                                                                                title="Upload an image to edit or use as reference for generating variations">
                                                                            <i class="bi bi-upload"></i> Upload Image
                                                                        </button>
                                                                        <input type="file" id="imagenImageInput" accept="image/*" style="display: none;">
                                                                    </div>
                                                                </div>

                                                                <div class="form-group mb-1">
                                                                    <label for="imagenMode" class="form-label mb-1">Generation Mode:</label>
                                                                    <select id="imagenMode" class="form-select form-select-sm" title="Text to Image: Generate from text prompt only&#10;Image to Image: Create variations of the uploaded image&#10;Image Editing: Edit the uploaded image based on your prompt">
                                                                        <option value="text-to-image" title="Generate images from text prompt only" selected>Text to Image</option>
                                                                        <option value="image-to-image" title="Create variations of the uploaded image">Image to Image</option>
                                                                        <option value="image-edit" title="Edit the uploaded image based on your text instructions">Image Editing</option>
                                                                    </select>
                                                                </div>

                                                                <div class="form-group mb-1">
                                                                    <label for="imagenPrompt" class="form-label mb-1" title="Text to Image: Required - Describe what you want to generate&#10;Image to Image: Optional - Guide the variation or leave empty&#10;Image Editing: Required - Describe the edits you want to make">
                                                                        Prompt: <small class="text-muted" id="promptRequiredText">(Required)</small>
                                                                    </label>
                                                                    <textarea id="imagenPrompt" class="form-control" rows="3"
                                                                        placeholder="Describe the image you want to generate or edit..."></textarea>
                                                                </div>
                                                                <div class="form-group mb-1">
                                                                    <label for="imagenNegativePrompt" class="form-label mb-1 text-danger" title="Specify elements you want to avoid in the generated image">
                                                                        Negative Prompt (Optional):
                                                                    </label>
                                                                    <textarea id="imagenNegativePrompt" class="form-control" rows="2"
                                                                        placeholder="Elements you want to exclude from the image..."></textarea>
                                                                </div>

                                                                <div class="form-group mb-1">
                                                                    <label for="imagenModel" class="form-label mb-1" title="Gemini models are free but have limited control over style and size. They require text output alongside images.&#10;Imagen models provide high-quality images with full control over style and size but require a paid API key.">
                                                                        Model:
                                                                    </label>
                                                                    <select id="imagenModel" class="form-select form-select-sm">
                                                                        <option value="gemini-2.0-flash-preview-image-generation" title="Free model with good quality and fast generation" selected>Gemini 2.0 Flash (Free)</option>
                                                                        <option value="imagen-3.0-generate-002" title="Paid model with high quality and more control">Imagen 3.0 Generate (Paid)</option>
                                                                        <option value="imagen-4.0-generate-preview-06-06" title="Latest Imagen 4 model with highest quality">Imagen 4.0 Generate (Paid)</option>
                                                                        <option value="imagen-3.0-fast-generate-002" title="Paid model with faster generation and good quality">Imagen 3.0 Fast Generate (Paid)</option>
                                                                    </select>
                                                                </div>
                                                                <div class="row g-2">
                                                                    <div class="col-md-6">
                                                                        <div class="form-group mb-1">
                                                                            <label for="imagenCount" class="form-label mb-1">
                                                                                Number of Images:
                                                                            </label>
                                                                            <select id="imagenCount" class="form-select form-select-sm" title="Number of images to generate in a single request. More images provide more variety.">
                                                                                <option value="1">1 Image</option>
                                                                                <option value="2">2 Images</option>
                                                                                <option value="4" selected>4 Images</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group mb-1">
                                                                            <label for="imagenSize" class="form-label mb-1" title="Image resolution. Only applies to Imagen models. Gemini models use a fixed size.">
                                                                                Image Size:
                                                                            </label>
                                                                            <select id="imagenSize" class="form-select form-select-sm">
                                                                                <option value="1024x1024" selected>1024x1024 (Square)</option>
                                                                                <option value="1024x768">1024x768 (Landscape)</option>
                                                                                <option value="768x1024">768x1024 (Portrait)</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="form-group mb-1">
                                                                    <label for="imagenStyle" class="form-label mb-1" title="Apply a specific visual style to the generated image. Works best with Imagen models.">
                                                                        Style Preset:
                                                                    </label>
                                                                    <select id="imagenStyle" class="form-select form-select-sm">
                                                                        <option value="none" selected>None</option>
                                                                        <option value="photographic" title="Realistic photographic style">Photographic</option>
                                                                        <option value="digital-art" title="Digital art with vibrant colors and sharp details">Digital Art</option>
                                                                        <option value="cinematic" title="Movie-like quality with dramatic lighting">Cinematic</option>
                                                                        <option value="anime" title="Japanese animation style">Anime</option>
                                                                        <option value="painting" title="Traditional painting appearance">Painting</option>
                                                                    </select>
                                                                </div>

                                                                <div class="d-grid gap-2 mt-2">
                                                                    <button id="generateImageBtn" class="btn btn-primary-purple text-white bi bi-magic" title="Image generation uses the same Gemini API key configured in the Settings tab."> Generate Images</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="table" role="tabpanel" aria-labelledby="table-tab">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0 bi bi-file-earmark-text"> CSV Files</h5>
                                            </div>
                                            <div class="card-body p-0">
                                                <div class="mb-3 p-3">
                                                    <label for="csvFileInput" class="form-label">Import CSV File</label>
                                                    <input type="file" class="form-control" id="csvFileInput"
                                                        accept=".csv" multiple="">
                                                    <small class="form-text text-muted">Import one or multiple CSV
                                                        files</small>
                                                </div>
                                                <ul id="csvFilesList" class="list-group list-group-flush">
                                                    <li class="list-group-item text-center text-muted">No CSV files
                                                        imported</li>
                                                </ul>
                                                <div class="p-3">
                                                    <button id="deleteAllCsvBtn"
                                                        class="btn btn-sm btn-danger w-100 bi bi-trash"> Delete All
                                                        Files</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h5 class="card-title mb-0 bi bi-table" id="currentCsvTitle"> CSV Table</h5>
                                                <div class="btn-group">
                                                    <button id="exportCsvBtn" class="btn btn-success btn-sm bi bi-download"> Export</button>
                                                    <button id="clearCsvBtn" class="btn btn-danger btn-sm bi bi-trash"> Clear</button>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div id="csvTableToolbar">
                                                    <div class="btn-group mb-2">
                                                        <button id="csvNewTableButton" class="btn btn-sm btn-success bi bi-file-earmark-plus"> New Table</button>
                                                        <button id="addRowBtn" class="btn btn-sm btn-primary bi bi-plus-circle"> Add Row</button>
                                                        <button id="deleteSelectedRowsBtn" class="btn btn-sm btn-danger bi bi-trash"> Delete Selected</button>
                                                        <button id="duplicateSelectedRowsBtn" class="btn btn-sm btn-info bi bi-files"> Duplicate Selected</button>
                                                        <button id="editSelectedRowsBtn" class="btn btn-sm btn-warning bi bi-pencil"> Edit Selected</button>
                                                        <button id="undoBtn" class="btn btn-sm btn-secondary bi bi-arrow-counterclockwise"> Undo</button>
                                                        <button id="redoBtn" class="btn btn-sm btn-secondary bi bi-arrow-clockwise"> Redo</button>
                                                    </div>
                                                </div>
                                                <div id="csvTable"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                                <div class="settings-subtabs mb-3">
                                    <ul class="nav nav-pills mb-3 d-flex align-items-center">
                                        <li class="nav-item">
                                            <a class="nav-link settings-subtab bi bi-key active" href="#"
                                                data-target="apiSettings"> API Settings</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link settings-subtab bi bi-chat-square-text" href="#"
                                                data-target="promptSettings"> Prompts</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link settings-subtab bi bi-file-earmark-text" href="#"
                                                data-target="templateSettings"> CSV Templates</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link settings-subtab bi bi-window-stack" href="#"
                                                data-target="widgetSettings"> Widgets</a>
                                        </li>
                                        <li class="nav-item ms-auto">
                                            <a class="nav-link bi bi-check2-circle" href="#" id="applySettingsBtn">
                                                Apply Settings</a>
                                        </li>
                                    </ul>
                                </div>
                                <div id="apiSettings" class="settings-subcontent active">
                                    <div class="card mb-3">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0 bi bi-gear"> API Settings</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <label class="form-label bi bi-key mb-0"> API Keys</label>
                                                    <div class="d-flex align-items-center gap-2">
                                                        <a href="https://aistudio.google.com/app/u/3/apikey" target="_blank"
                                                           class="btn btn-sm btn-outline-primary"
                                                           title="Get API Key from Google AI Studio">
                                                            <i class="bi bi-plus-circle me-1"></i>Get API Key
                                                        </a>
                                                        <span class="badge bg-primary" id="apiKeysCount">0 keys</span>
                                                    </div>
                                                </div>
                                                <div class="input-group mb-2">
                                                    <select class="form-select" id="apiKeysList">
                                                        <!-- API keys will be listed here -->
                                                        <option value="" disabled selected>No API keys saved</option>
                                                    </select>
                                                    <button class="btn btn-success" id="newApiKeyBtn" title="Add new API key">
                                                        <i class="bi bi-plus-circle"></i>
                                                    </button>
                                                    <button class="btn btn-danger" id="deleteApiKeyBtn" title="Delete selected API key">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                                <div class="input-group mb-2">
                                                    <input type="text" class="form-control" id="apiKeyName" placeholder="API Name (e.g. My Gemini Account)">
                                                </div>
                                                <div class="input-group mb-2">
<div class="input-group">
    <input type="password" class="form-control" id="apiKey" placeholder="Enter your Gemini API key">
    <button class="btn btn-outline-secondary" type="button" id="toggleApiKeyVisibility" tabindex="-1">
        <i class="bi bi-eye" id="apiKeyVisibilityIcon"></i>
    </button>
    <button class="btn btn-primary" id="addApiKeyBtn">Add</button>
    <button class="btn btn-secondary" id="cancelEditBtn" style="display: none;">Cancel</button>
</div>
                                                </div>
                                                <div class="btn-group w-100 mb-2">
                                                    <button id="exportApiKeysBtn" class="btn btn-info bi bi-download"> Export Keys</button>
                                                    <button id="importApiKeysBtn" class="btn btn-info bi bi-upload"> Import Keys</button>
                                                    <button id="deleteAllApiKeysBtn" class="btn btn-danger bi bi-trash"> Delete All</button>
                                                    <input type="file" id="apiKeysFileInput" accept=".json" style="display: none;">
                                                </div>
                                                <div class="form-check form-switch mb-2">
                                                    <input class="form-check-input" type="checkbox" id="enableApiKeyRotation">
                                                    <label class="form-check-label" for="enableApiKeyRotation">
                                                        Enable API Key Rotation
                                                    </label>
                                                    <small class="d-block text-muted">
                                                        If enabled, the system will automatically try the next API key when the current one fails.
                                                    </small>
                                                </div>
                                                <small class="form-text text-muted">
                                                    <i class="bi bi-info-circle text-primary-purple" title="API keys are stored locally in your browser and never sent to any server."></i>
                                                    API keys are stored locally in your browser.
                                                </small>
                                            </div>
                                            <div class="mb-3">
                                                <label for="modelSelect" class="form-label">Gemini Model</label>
                                                <select class="form-control" id="modelSelect">
                                                    <option value="gemini-2.0-flash">Gemini 2.0 Flash (Fast)</option>
                                                    <option value="gemini-1.5-pro">Gemini 1.5 Pro (Balanced)</option>
                                                    <option value="gemini-2.0-pro">Gemini 2.0 Pro (High Quality)</option>
                                                    <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
                                                    <option value="gemini-2.5-flash">Gemini 2.5 Flash</option>
                                                    <option value="gemini-2.5-flash-lite-preview-06-17">Gemini 2.5 Flash Lite Preview</option>
                                                    <option value="gemini-2.5-flash-preview-native-audio-dialog">Gemini 2.5 Flash Audio Dialog</option>
                                                    <option value="gemini-2.5-flash-exp-native-audio-thinking-dialog">Gemini 2.5 Flash Audio Thinking</option>
                                                    <option value="gemini-2.5-flash-preview-tts">Gemini 2.5 Flash TTS</option>
                                                    <option value="gemini-2.5-pro-preview-tts">Gemini 2.5 Pro TTS</option>
                                                    <option value="gemini-2.0-flash-preview-image-generation">Gemini 2.0 Flash Image Gen</option>
                                                </select>
                                                <small class="form-text text-muted">
                                                    <i class="bi bi-info-circle text-primary-purple" title="Select model based on your needs. Flash for speed, Pro for accuracy, Pro 2.0 for highest quality results."></i>
                                                    Select model based on your needs.
                                                </small>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="concurrencySlider" class="form-label">Concurrent
                                                        Requests: <span id="concurrencyValue">3</span></label>
                                                    <input type="range" class="form-range" id="concurrencySlider"
                                                        min="1" max="10" step="1" value="3">
                                                    <small class="form-text text-muted">
                                                        <i class="bi bi-info-circle text-primary-purple" title="Number of images processed simultaneously"></i>
                                                        Number of images processed simultaneously
                                                    </small>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="rateSlider" class="form-label">Requests Per Second:
                                                        <span id="rateValue">5</span></label>
                                                    <input type="range" class="form-range" id="rateSlider" min="1"
                                                        max="10" step="1" value="5">
                                                    <small class="form-text text-muted">
                                                        <i class="bi bi-info-circle text-primary-purple" title="API request rate limit"></i>
                                                        API request rate limit
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <h6 class="form-label">Processing Settings</h6>
                                                <div class="mt-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox"
                                                            id="enableParallelProcessing" checked="">
                                                        <label class="form-check-label" for="enableParallelProcessing">
                                                            Enable Parallel Processing
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-switch mt-2">
                                                        <input class="form-check-input" type="checkbox"
                                                            id="enableDirectVideoProcessing" checked="">
                                                        <label class="form-check-label" for="enableDirectVideoProcessing">
                                                            Enable Direct Video Processing
                                                        </label>
                                                        <small class="d-block text-muted">
                                                            If enabled, videos will be processed directly by Gemini API (requires Gemini 2.0 models).
                                                            If disabled, videos will be processed using extracted thumbnails.
                                                        </small>
                                                    </div>
                                                </div>
                                                <!-- Web Workers Settings removed as parallel processing now handles workers efficiently -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="promptSettings" class="settings-subcontent">
                                    <div class="card mb-4 prompt-manager-section">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0 bi bi-chat-square-text"> Prompt Manager</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mb-1">
                                                <div class="col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label for="promptName" class="form-label">Prompt Name:</label>
                                                        <input type="text" id="promptName" class="form-control"
                                                            placeholder="My Custom Prompt">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="saved-prompts-container">
                                                        <label for="promptList" class="form-label">Saved
                                                            Prompts:</label>
                                                        <select id="promptList" class="form-select">
                                                            <option>Detailed Description</option>
                                                            <option>Product Listing</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mb-4">
                                                <div class="col-md-6">
                                                    <div class="btn-group w-100">
                                                        <button id="savePromptBtn" class="btn btn-primary bi bi-save">
                                                            Save</button>
                                                        <button id="exportPromptBtn"
                                                            class="btn btn-info bi bi-download"> Export JSON</button>
                                                        <button id="importPromptBtn" class="btn btn-info bi bi-upload">
                                                            Import JSON</button>
                                                    </div>
                                                    <input type="file" id="promptFileInput" accept=".json"
                                                        style="display: none;">
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="btn-group w-100">

                                                        <button id="deletePromptBtn" class="btn btn-danger bi bi-trash">
                                                            Delete</button>
                                                        <button id="resetPromptsBtn" class="btn btn-secondary bi bi-arrow-counterclockwise">
                                                            Reset</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- Main Row with 3 Columns -->
                                            <div class="row mb-1">
                                                <!-- Title Column -->
                                                <div class="col-md-4 mb-3">
                                                    <h6 class="border-bottom pb-1">Title Settings</h6>
<div class="form-group mb-2">
    <label for="titleLengthSlider" class="form-label">Title Length: <span
            id="titleLengthValue">10</span>
        words</label>
    <div class="d-flex align-items-center">
        <input type="range" class="form-range flex-grow-1 me-2"
            id="titleLengthSlider" min="3" max="20" step="1"
            value="10">
        <input type="number" class="form-control form-control-sm" id="titleLengthInput" min="3" max="20" value="10" style="width:70px;">
    </div>
</div>
                                                    <div class="form-group mb-2">
                                                        <label for="positiveTitlePrompt" class="form-label text-success">Positive Prompt:</label>
                                                        <textarea id="positiveTitlePrompt" class="form-control" rows="4"
                                                            placeholder="Enter positive prompt for title generation..."></textarea>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="negativeTitlePrompt" class="form-label text-danger">Negative Prompt:</label>
                                                        <textarea id="negativeTitlePrompt" class="form-control" rows="4"
                                                            placeholder="Enter negative prompt for title generation..."></textarea>
                                                    </div>
                                                </div>

                                                <!-- Description Column -->
                                                <div class="col-md-4 mb-3">
                                                    <h6 class="border-bottom pb-1">Description Settings</h6>
<div class="form-group mb-2">
    <label for="descriptionLengthSlider"
        class="form-label">Description Length: <span
            id="descriptionLengthValue">250</span>
        characters</label>
    <div class="d-flex align-items-center">
        <input type="range" class="form-range flex-grow-1 me-2"
            id="descriptionLengthSlider" min="10" max="500" step="1"
            value="250">
        <input type="number" class="form-control form-control-sm" id="descriptionLengthInput" min="10" max="500" value="250" style="width:90px;">
    </div>
</div>
                                                    <div class="form-group mb-2">
                                                        <label for="positiveDescriptionPrompt" class="form-label text-success">Positive Prompt:</label>
                                                        <textarea id="positiveDescriptionPrompt" class="form-control" rows="4"
                                                            placeholder="Enter positive prompt for description generation..."></textarea>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="negativeDescriptionPrompt" class="form-label text-danger">Negative Prompt:</label>
                                                        <textarea id="negativeDescriptionPrompt" class="form-control" rows="4"
                                                            placeholder="Enter negative prompt for description generation..."></textarea>
                                                    </div>
                                                </div>

                                                <!-- Keywords Column -->
                                                <div class="col-md-4 mb-3">
                                                    <h6 class="border-bottom pb-1">Keywords Settings</h6>
<div class="form-group mb-2">
    <label for="keywordsCountSlider" class="form-label">Keywords
        Count: <span id="keywordsCountValue">20</span>
        keywords</label>
    <div class="d-flex align-items-center">
        <input type="range" class="form-range flex-grow-1 me-2" id="keywordsCountSlider"
            min="5" max="50" step="1" value="20">
        <input type="number" class="form-control form-control-sm" id="keywordsCountInput" min="5" max="50" value="20" style="width:70px;">
    </div>
</div>
                                                    <div class="form-group mb-2">
                                                        <label for="positiveKeywordsPrompt" class="form-label text-success">Positive Prompt:</label>
                                                        <textarea id="positiveKeywordsPrompt" class="form-control" rows="4"
                                                            placeholder="Enter positive prompt for keywords generation..."></textarea>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="negativeKeywordsPrompt" class="form-label text-danger">Negative Prompt:</label>
                                                        <textarea id="negativeKeywordsPrompt" class="form-control" rows="4"
                                                            placeholder="Enter negative prompt for keywords generation..."></textarea>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Legacy fields for backward compatibility -->
                                            <div style="display: none;">
                                                <textarea id="positivePrompt"></textarea>
                                                <textarea id="negativePrompt"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="templateSettings" class="settings-subcontent">
                                    <div class="card mb-3">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0 bi bi-file-earmark-text"> CSV Templates</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <div class="btn-group w-100 mb-2">
                                                    <button id="addTemplateBtn" class="btn btn-primary bi bi-plus-circle">
                                                        Add Template</button>
                                                    <button id="exportAllTemplatesBtn" class="btn btn-info bi bi-download">
                                                        Export All</button>
                                                    <button id="importTemplateJsonBtn" class="btn btn-info bi bi-upload">
                                                        Import JSON</button>
                                                    <button id="deleteAllTemplatesBtn" class="btn btn-danger bi bi-trash" onclick="window.deleteAllTemplates()">
                                                        Delete All</button>
                                                </div>
                                                <input type="file" id="templateJsonFileInput" accept=".json" style="display: none;">
                                            </div>
                                            <div id="templateList" class="list-group mt-2">
                                                <div
                                                    class="list-group-item d-flex justify-content-between align-items-center bg-light">
                                                    <div class="d-flex align-items-center">
                                                        <div class="form-check form-switch me-3"><input type="checkbox"
                                                                class="form-check-input" id="toggleAllTemplates"></div>
                                                        Toggle All
                                                    </div>
                                                    <div class="d-flex align-items-center gap-2">
                                                        <span class="badge bg-secondary" title="Inactive templates"><i class="bi bi-dash-circle"></i> <span id="inactiveTemplateCount">0</span></span>
                                                        <span class="badge bg-success" title="Active templates"><i class="bi bi-check-circle"></i> <span id="activeTemplateCount">0</span></span>
                                                        <span class="badge bg-primary" title="Total templates"><i class="bi bi-files"></i> <span id="totalTemplateCount">0</span></span>
                                                    </div>

                                                        <!-- Template List -->
                                                    <div id="templateList" class="list-group mt-2"></div>
                                                </div>
                                                <div
                                                    class="list-group-item d-flex justify-content-between align-items-center">
                                                </div>
                                            </div>
                                            <div class="mt-3 small text-muted">
                                                <i class="bi bi-info-circle text-primary-purple" title="Import CSV templates to customize export formats. Templates should contain only header rows."></i>
                                                Import CSV templates to customize export formats.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="widgetSettings" class="settings-subcontent">
                                    <div class="card mb-3">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0 bi bi-window-stack"> Widget Settings</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mb-4">
                                                <div class="col-md-6">
                                                    <h6 class="border-bottom pb-1">Chat Widget</h6>
                                                    <div class="form-check form-switch mb-3">
                                                        <input class="form-check-input" type="checkbox" id="toggleChatWidget" checked>
                                                        <label class="form-check-label" for="toggleChatWidget">
                                                            Show Chat Widget
                                                        </label>
                                                    </div>
                                                    <button id="resetChatWidgetPosition" class="btn btn-secondary bi bi-arrow-counterclockwise"> Reset Position</button>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6 class="border-bottom pb-1">Floating Action Button</h6>
                                                    <div class="form-check form-switch mb-3">
                                                        <input class="form-check-input" type="checkbox" id="toggleFloatingBtn" checked>
                                                        <label class="form-check-label" for="toggleFloatingBtn">
                                                            Show Floating Button
                                                        </label>
                                                    </div>
                                                    <button id="resetFloatingBtnPosition" class="btn btn-secondary bi bi-arrow-counterclockwise"> Reset Position</button>
                                                </div>
                                            </div>
                                            <div class="mt-3 small text-muted">
                                                <i class="bi bi-info-circle text-primary-purple" title="Both widgets can be dragged to any position on the screen. Use the reset buttons to restore their default positions."></i>
                                                Both widgets can be dragged to any position on the screen.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Table Modal -->
    <div class="modal fade" id="newTableModal" tabindex="-1" aria-labelledby="newTableModalLabel" data-accessibility-fix="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header text-white" style="background-color: var(--primary-purple);">
                    <h5 class="modal-title" id="newTableModalLabel">Create New Table</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="tableTemplateSelect" class="form-label">Select Template:</label>
                        <select id="tableTemplateSelect" class="form-select">
                            <option value="Custom" selected>Custom</option>
                            <option value="Shutterstock">Shutterstock</option>
                            <option value="Adobe Stock">Adobe Stock</option>
                            <option value="Canva">Canva</option>
                            <option value="Freepik">Freepik</option>
                            <option value="Vecteezy">Vecteezy</option>
                            <option value="Dreamstime">Dreamstime</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="tableNameInput" class="form-label">Table Name:</label>
                        <input type="text" class="form-control" id="tableNameInput" placeholder="Enter table name">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Headers:</label>
                        <div id="headerInputs" class="header-inputs">
                            <!-- Header inputs will be added here -->
                        </div>
                        <button type="button" class="btn btn-primary mt-2" id="addHeaderBtn">
                            <i class="bi bi-plus-circle"></i> Add Header
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancelTableButton" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn" id="createTableButton" style="background-color: var(--primary-purple); color: white;">Create Table</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Edit Modal -->
    <div class="modal fade" id="bulkEditModal" tabindex="-1" aria-labelledby="bulkEditModalLabel">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="bulkEditModalLabel">Edit Selected Rows</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="bulkEditForm">
                        <div class="mb-3">
                            <label for="columnSelect" class="form-label">Select Column to Edit</label>
                            <select class="form-select" id="columnSelect">
                                <option value="">-- Select Column --</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="newValue" class="form-label">New Value</label>
                            <textarea class="form-control" id="newValue" rows="3" placeholder="Enter new value"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="applyBulkEdit">Apply Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- More Templates Modal -->
    <div class="modal fade" id="moreTemplatesModal" tabindex="-1" aria-labelledby="moreTemplatesModalLabel">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="moreTemplatesModalLabel">Template Manager</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row" id="templateModalContent">
                        <!-- Template items will be inserted here dynamically -->
                    </div>
                </div>
                <div class="modal-footer">
                    <span class="template-count" id="templateCountBadge">0 Templates</span>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Delete Confirmation Modal -->
    <div class="modal fade" id="deleteTemplateModal" tabindex="-1" aria-labelledby="deleteTemplateModalLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteTemplateModalLabel">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this template?</p>
                    <p class="fw-bold" id="templateToDeleteName"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteTemplate">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <!-- Prompt Load Confirmation Modal -->
    <div class="modal fade" id="replacePromptModal" tabindex="-1" aria-labelledby="replacePromptModalLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="replacePromptModalLabel">Confirm Replace</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>This will replace your current prompt text. Continue?</p>
                    <p class="fw-bold" id="promptToReplaceName"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" id="confirmReplacePrompt">Replace</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Prompt Overwrite Confirmation Modal -->
    <div class="modal fade" id="overwritePromptModal" tabindex="-1" aria-labelledby="overwritePromptModalLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="overwritePromptModalLabel">Confirm Overwrite</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Prompt "<span id="promptToOverwriteName"></span>" already exists. Do you want to overwrite it?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmOverwritePrompt">Overwrite</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Prompt Overwrite Confirmation Modal -->
    <div class="modal fade" id="importOverwriteModal" tabindex="-1" aria-labelledby="importOverwriteModalLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="importOverwriteModalLabel">Confirm Import Overwrite</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Prompt "<span id="importPromptName"></span>" already exists. Do you want to overwrite it?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-info" id="confirmImportOverwrite">Overwrite</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reset Prompts Confirmation Modal -->
    <div class="modal fade" id="resetPromptsModal" tabindex="-1" aria-labelledby="resetPromptsModalLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-secondary text-white">
                    <h5 class="modal-title" id="resetPromptsModalLabel">Confirm Reset</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>This will reset all prompts to default values. Any custom prompts will be lost. Continue?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmResetPrompts">Reset</button>
                </div>
            </div>
        </div>
    </div>

    <!-- API Key modals are created dynamically in settings-manager.js -->

    <!-- Export CSV Modal -->
    <div class="modal fade" id="exportCsvModal" tabindex="-1" aria-labelledby="exportCsvModalLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary-purple text-white">
                    <h5 class="modal-title" id="exportCsvModalLabel">Export CSV</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="csvFileNameInput" class="form-label">File Name:</label>
                        <input type="text" class="form-control" id="csvFileNameInput" placeholder="Enter file name">
                    </div>
                    <div class="mb-3">
                        <label for="csvExportOptions" class="form-label">Export Options:</label>
                        <select class="form-select" id="csvExportOptions">
                            <option value="all">All</option>
                            <option value="custom">Custom</option>
                        </select>
                    </div>
                    <div id="csvCheckboxContainer" class="mb-3">
                        <div class="form-check mb-2">
                            <input class="form-check-input csvExportCheckbox" type="checkbox" value="Shutterstock" id="checkShutterstock" checked>
                            <label class="form-check-label" for="checkShutterstock">Shutterstock</label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input csvExportCheckbox" type="checkbox" value="Adobe Stock" id="checkAdobeStock" checked>
                            <label class="form-check-label" for="checkAdobeStock">Adobe Stock</label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input csvExportCheckbox" type="checkbox" value="Canva" id="checkCanva" checked>
                            <label class="form-check-label" for="checkCanva">Canva</label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input csvExportCheckbox" type="checkbox" value="Freepik" id="checkFreepik" checked>
                            <label class="form-check-label" for="checkFreepik">Freepik</label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input csvExportCheckbox" type="checkbox" value="Vecteezy" id="checkVecteezy" checked>
                            <label class="form-check-label" for="checkVecteezy">Vecteezy</label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input csvExportCheckbox" type="checkbox" value="Dreamstime" id="checkDreamstime" checked>
                            <label class="form-check-label" for="checkDreamstime">Dreamstime</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary-purple" id="confirmCsvExport">Export</button>
                </div>
            </div>
        </div>
    </div>

    <script src="bootstrap/bootstrap.bundle.min.js"></script>
    <script src="tabulator-master/dist/js/tabulator.min.js"></script>
    <script src="papaparse/papaparse.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

    <!-- Core and Utils -->
    <script src="js/toast.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/class.js"></script>
    <script src="js/core.js"></script>
    <script src="js/floating-button.js"></script>
    <script src="js/chat-widget.js"></script>

    <!-- Managers and Processors -->
    <script src="js/video-processor.js"></script>
    <script src="js/gemini-video-api.js"></script>
    <script src="js/api.js"></script>
    <script src="js/parallel-processor-loader.js"></script>
    <script src="js/settings-manager.js"></script>
    <script src="js/csv-template.js"></script>
    <script src="js/csv-manager.js"></script>
    <script src="js/image-preview.js"></script>
    <script src="js/regenerate.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/prompt-manager.js"></script>
    <script src="js/imagen-generator.js"></script>

    <!-- Metadata Handling -->
    <script src="js/file-utils.js"></script>
    <script src="js/metadata-manager.js"></script>
    <script src="js/metadata-dialog.js"></script>

    <!-- Format and Clear -->
    <script src="js/clear-format.js"></script>
    <script src="js/platform-export.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="js/toast-manager.js"></script>
    <script src="js/prompter-structured-data.js"></script>
    <script src="js/prompter-generator.js"></script>
    <script src="js/draggable.js"></script>
    <script src="js/widget-settings.js"></script>
    <script src="js/knowledge-base.js"></script>
    <script>
        // Initialize CSV Manager when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a short time to ensure all scripts are loaded
            setTimeout(function() {
                if (typeof window.initCsvManager === 'function') {
                    window.initCsvManager();
                    console.log('CSV Manager initialized successfully');
                } else {
                    console.error('initCsvManager function not found');
                }
            }, 100);
        });
    </script>
</body>

</html>
